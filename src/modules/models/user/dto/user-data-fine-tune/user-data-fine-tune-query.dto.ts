import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';
import { DataFineTuneStatus } from '@/modules/models/constants/data-fine-tune-status.enum';

export enum UserDataFineTuneSortBy {
  CREATED_AT = 'createdAt',
  NAME = 'name',
}

/**
 * DTO cho việc truy vấn danh sách user data fine-tune
 */
export class UserDataFineTuneQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo trạng thái',
    enum: DataFineTuneStatus,
    example: DataFineTuneStatus.PENDING,
    required: false
  })
  @IsOptional()
  @IsString()
  status?: DataFineTuneStatus;  

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: UserDataFineTuneSortBy,
    example: UserDataFineTuneSortBy.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: UserDataFineTuneSortBy = UserDataFineTuneSortBy.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsString()
  sortDirection?: SortDirection = SortDirection.DESC;
}
