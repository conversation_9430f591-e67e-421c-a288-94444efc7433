import { Injectable, ExecutionContext } from '@nestjs/common';
import { JwtUtilService } from './jwt.util';
import { RedisService } from '@/shared/services/redis.service';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Reflector } from '@nestjs/core';

@Injectable()
export class JwtUserGuard extends RolesGuard {
  constructor(
    reflector: Reflector,
    jwtUtilService: JwtUtilService,
    redisService: RedisService,
  ) {
    super(jwtUtilService, redisService, reflector);
  }
}
