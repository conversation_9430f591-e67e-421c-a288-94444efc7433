import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
  IsBoolean,
  IsArray,
} from 'class-validator';
import { AgentTemplateStatus } from '@modules/agent/constants';
import { ConvertConfig, ConversionConfig } from '@modules/agent/interfaces/convert-config.interface';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { AgentMemory } from '@modules/agent/interfaces/agent-memory.interface';
import { ModelConfigDto } from '../agent-system';

/**
 * DTO cho việc tạo agent template mới
 */
export class CreateAgentTemplateDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig: ModelConfigDto;

  /**
   * Thông tin hồ sơ mẫu
   */
  @ApiPropertyOptional({
    description: 'Thông tin hồ sơ mẫu',
    type: Object,
    example: {
      gender: 'MALE',
      dateOfBirth: '1990-01-01',
      position: 'Developer',
      education: 'Bachelor',
      skills: ['JavaScript', 'Python'],
      personality: ['Creative', 'Team-player'],
      languages: ['English', 'Vietnamese'],
      nations: 'Vietnam'
    }
  })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  profile?: ProfileAgent;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Trạng thái của agent template
   */
  @ApiProperty({
    description: 'Trạng thái của agent template',
    enum: AgentTemplateStatus,
    example: AgentTemplateStatus.DRAFT,
  })
  @IsEnum(AgentTemplateStatus)
  status: AgentTemplateStatus;

  /**
   * Cấu hình chuyển đổi
   */
  @ApiPropertyOptional({
    description: 'Cấu hình chuyển đổi',
    type: Object,
    example: {
      name: 'Tên cấu hình chuyển đổi',
      description: 'Mô tả cấu hình chuyển đổi'
    }
  })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  convertConfig?: ConvertConfig;

  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  @IsNumber()
  typeId: number;

  /**
   * ID của base model (system model)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (system model)',
    example: 'base-model-uuid',
  })
  @IsString()
  @IsOptional()
  modelBaseId?: string;



  /**
   * Trạng thái có thể bán
   */
  @ApiPropertyOptional({
    description: 'Trạng thái có thể bán',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isForSale?: boolean;
}
