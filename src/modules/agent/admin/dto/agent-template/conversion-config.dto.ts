import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsIn, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho cấu hình conversion
 */
export class ConversionConfigDto {
  /**
   * Tên của field trong schema JSON
   */
  @ApiProperty({
    description: 'Tên của field trong schema JSON',
    example: 'email',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * Kiểu dữ liệu của field
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu của field',
    enum: ['string', 'number', 'boolean', 'array_number', 'array_string', 'enum'],
    example: 'string',
  })
  @IsString()
  @IsIn(['string', 'number', 'boolean', 'array_number', 'array_string', 'enum'])
  type: 'string' | 'number' | 'boolean' | 'array_number' | 'array_string' | 'enum';

  /**
   * <PERSON><PERSON>ả (nội dung) của field
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả (nội dung) của field',
    example: 'Địa chỉ email người dùng',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  @ApiProperty({
    description: 'Trường này có bắt buộc không?',
    example: true,
  })
  @IsBoolean()
  required: boolean;

  /**
   * Trạng thái hoạt động của field
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của field',
    example: true,
  })
  @IsBoolean()
  active: boolean;
}
