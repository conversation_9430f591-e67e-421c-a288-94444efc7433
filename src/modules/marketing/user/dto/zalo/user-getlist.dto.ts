import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';

/**
 * Enum cho khoảng thời gian tương tác gần nhất
 */
export enum LastInteractionPeriod {
  TODAY = 'TODAY',
  YESTERDAY = 'YESTERDAY',
  L7D = 'L7D',
  L30D = 'L30D',
}

/**
 * DTO cho yêu cầu lấy danh sách người dùng
 */
export class UserGetListRequestDto {
  @ApiProperty({
    description: 'Thứ tự của người dùng đầu tiên trong danh sách trả về (tối đa 9951)',
    example: 0,
    minimum: 0,
    maximum: 9951,
  })
  @IsNumber()
  @Min(0)
  @Max(9951)
  offset: number;

  @ApiProperty({
    description: '<PERSON><PERSON> lượng người dùng muốn lấy (tối đa 50 người dùng 1 request)',
    example: 20,
    minimum: 1,
    maximum: 50,
  })
  @IsNumber()
  @Min(1)
  @Max(50)
  count: number;

  @ApiPropertyOptional({
    description: 'Tên của nhãn được gắn cho người dùng',
    example: 'VIP_CUSTOMER',
  })
  @IsOptional()
  @IsString()
  tagName?: string;

  @ApiPropertyOptional({
    description: 'Khoảng thời gian tương tác gần nhất của người dùng',
    enum: LastInteractionPeriod,
    example: LastInteractionPeriod.L7D,
  })
  @IsOptional()
  @IsEnum(LastInteractionPeriod)
  lastInteractionPeriod?: LastInteractionPeriod | string;

  @ApiPropertyOptional({
    description: 'Trạng thái quan tâm OA của người dùng (true: đang quan tâm, false: chưa quan tâm)',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isFollower?: boolean;
}

/**
 * DTO cho thông tin người dùng trong response
 */
export class UserGetListItemDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '4572947693969771653',
  })
  userId: string;
}

/**
 * DTO cho dữ liệu danh sách người dùng
 */
export class UserGetListDataDto {
  @ApiProperty({
    description: 'Tổng số lượng người dùng thỏa điều kiện',
    example: 6,
  })
  total: number;

  @ApiProperty({
    description: 'Số lượng người dùng đã request',
    example: 15,
  })
  count: number;

  @ApiProperty({
    description: 'Thứ tự của người dùng đầu tiên trong danh sách trả về',
    example: 0,
  })
  offset: number;

  @ApiProperty({
    description: 'Danh sách ID của tất cả người dùng thỏa điều kiện truyền vào',
    type: [UserGetListItemDto],
  })
  users: UserGetListItemDto[];
}

/**
 * DTO cho phản hồi API getlist
 */
export class UserGetListResponseDto {
  @ApiProperty({
    description: 'Dữ liệu danh sách người dùng',
    type: UserGetListDataDto,
  })
  data: UserGetListDataDto;

  @ApiProperty({
    description: 'Mã lỗi (0 = thành công)',
    example: 0,
  })
  error: number;

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Success',
  })
  message: string;
}
