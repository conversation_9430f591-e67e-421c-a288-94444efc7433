import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';
import { BaseProductDto } from '../base-product/base-product.dto';
import { ServicePackageDto } from '../../advanced-info/service-advanced-info.dto';
import { HasPriceDto, StringPriceDto } from '../../price.dto';
import { ClassificationPriceDto, ClassificationStringPriceDto, CreateClassificationDto } from '../../classification.dto';
import { PriceTypeEnum } from '@modules/business/enums';

/**
 * DTO cho việc tạo sản phẩm dịch vụ (SERVICE)
 * Kế thừa từ BaseProductDto và thêm các trường đặc thù cho dịch vụ
 */
export class ServiceProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 0,
  })
  @IsNumber()
  @Min(0)
  purchaseCount: number;

  @ApiProperty({
    description: 'Danh sách gói dịch vụ',
    type: [ServicePackageDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ServicePackageDto)
  servicePackages: ServicePackageDto[] = [];

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [CreateClassificationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateClassificationDto)
  classifications?: CreateClassificationDto[];

  @ApiProperty({
    description: 'Loại giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  @IsNotEmpty()
  typePrice: PriceTypeEnum;

  // Override price để bắt buộc cho SERVICE
  @ApiProperty({
    description: 'Thông tin giá sản phẩm - Bắt buộc đối với sản phẩm dịch vụ',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' }
    ],
  })
  @IsNotEmpty()
  @IsObject()
  @ValidateNested()
  @Type(() => Object)
  price: HasPriceDto | StringPriceDto | ClassificationPriceDto | ClassificationStringPriceDto;

  // Service-specific fields for frontend compatibility
  @ApiProperty({
    description: 'Thời gian thực hiện dịch vụ (timestamp)',
    example: 1704067200000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  serviceTime?: number;

  @ApiProperty({
    description: 'Thời lượng dịch vụ (phút)',
    example: '60',
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceDuration?: string;

  @ApiProperty({
    description: 'Nhà cung cấp dịch vụ',
    example: 'Công ty tư vấn ABC',
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceProvider?: string;

  @ApiProperty({
    description: 'Loại dịch vụ',
    example: 'CONSULTATION',
    enum: ['CONSULTATION', 'TRAINING', 'SUPPORT', 'MAINTENANCE', 'OTHER'],
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceType?: string;

  @ApiProperty({
    description: 'Địa điểm thực hiện dịch vụ',
    example: 'AT_CENTER',
    enum: ['AT_CENTER', 'AT_CUSTOMER', 'ONLINE', 'HYBRID'],
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceLocation?: string;

  @ApiProperty({
    description: 'Mô tả dịch vụ',
    example: 'Dịch vụ tư vấn chuyên nghiệp',
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceDescription?: string;
}
