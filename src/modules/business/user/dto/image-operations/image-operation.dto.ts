import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  ValidateIf,
  Min,
  IsInt,
} from 'class-validator';

/**
 * Enum cho các loại thao tác hình ảnh
 */
export enum ImageOperationType {
  ADD = 'add',
  DELETE = 'delete',
  UPDATE = 'update',
}

/**
 * DTO cho thao tác hình ảnh sản phẩm - PHIÊN BẢN MỚI
 * Hỗ trợ xóa theo entity_has_media.id để tránh nhầm lẫn
 */
export class ProductImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác hình ảnh',
    enum: ImageOperationType,
    example: ImageOperationType.ADD,
  })
  @IsEnum(ImageOperationType)
  @IsNotEmpty()
  operation: ImageOperationType;

  @ApiProperty({
    description: 'Media ID từ kho media_data (bắt buộc cho thao tác ADD)',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @ValidateIf((o) => o.operation === ImageOperationType.ADD)
  @IsString()
  @IsNotEmpty()
  mediaId?: string;

  @ApiProperty({
    description: 'ID của bản ghi trong entity_has_media (bắt buộc cho thao tác DELETE)',
    example: 123,
    required: false,
  })
  @ValidateIf((o) => o.operation === ImageOperationType.DELETE)
  @IsInt()
  @Min(1)
  entityMediaId?: number;

  @ApiProperty({
    description: 'Vị trí hình ảnh (thứ tự hiển thị)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  position?: number;

  // ⚠️ DEPRECATED: Giữ lại để backward compatibility
  @ApiProperty({
    description: '⚠️ DEPRECATED: Sử dụng entityMediaId thay thế. Key hình ảnh (chỉ để tương thích ngược)',
    example: 'product-image-1.jpg',
    required: false,
    deprecated: true,
  })
  @IsOptional()
  @IsString()
  key?: string;
}
