### Test Update Physical Product
### Cập nhật sản phẩm vật lý với processors mới

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "<PERSON>o thun nam cao cấp - Updated",
  "price": {
    "listPrice": 350000,
    "salePrice": 300000,
    "currency": "VND"
  },
  "description": "Áo thun nam chất liệu cotton 100% cao cấp - <PERSON><PERSON><PERSON> bản cập nhật",
  "tags": ["thời trang", "nam", "áo thun", "cotton", "updated"],
  "shipmentConfig": {
    "widthCm": 26,
    "heightCm": 6,
    "lengthCm": 31,
    "weightGram": 220
  },
  "inventory": [
    {
      "availableQuantity": 120,
      "sku": "SHIRT-MEN-001-V2",
      "barcode": "1111111111112",
      "warehouseId": 1
    }
  ]
}

### Test Update Digital Product
### Cậ<PERSON> nhật sản phẩm số với delivery method mới

PUT {{baseUrl}}/user/products/2
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "<PERSON><PERSON><PERSON><PERSON> học lập trình Python - Updated",
  "price": {
    "listPrice": 1200000,
    "salePrice": 900000,
    "currency": "VND"
  },
  "description": "Khóa học lập trình Python từ cơ bản đến nâng cao - Phiên bản cập nhật",
  "tags": ["lập trình", "python", "khóa học", "online", "updated"],
  "deliveryMethod": "DASHBOARD",
  "deliveryTiming": "IMMEDIATE",
  "outputType": "CONTENT",
  "advancedInfo": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE",
    "outputType": "CONTENT",
    "variantMetadata": {
      "variants": [
        {
          "name": "Gói cơ bản - Updated",
          "price": {
            "listPrice": 900000,
            "salePrice": 700000,
            "currency": "VND"
          },
          "description": "Gói học cơ bản 3 tháng - Cập nhật",
          "metadata": {
            "duration": "3 tháng",
            "support": "Email + Chat",
            "certificate": "Có"
          }
        }
      ]
    }
  }
}

### Test Update Event Product
### Cập nhật sự kiện với ticket types mới

PUT {{baseUrl}}/user/products/3
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Workshop Marketing Digital 2024 - Updated",
  "description": "Workshop về marketing digital cho doanh nghiệp vừa và nhỏ - Phiên bản cập nhật",
  "tags": ["workshop", "marketing", "digital", "doanh nghiệp", "updated"],
  "eventDate": "2024-12-31T10:00:00Z",
  "eventLocation": "Tầng 6, Tòa nhà ABC, 123 Đường XYZ, Hà Nội",
  "maxAttendees": 120,
  "advancedInfo": {
    "ticketTypes": [
      {
        "name": "Vé Early Bird - Updated",
        "price": {
          "listPrice": 350000,
          "salePrice": 280000,
          "currency": "VND"
        },
        "description": "Vé ưu đãi sớm cho 60 người đầu tiên",
        "maxQuantity": 60,
        "metadata": {
          "benefits": ["Tài liệu in", "Coffee break", "Networking lunch", "Gift"],
          "validUntil": "2024-11-30"
        }
      },
      {
        "name": "Vé VIP Premium",
        "price": {
          "listPrice": 800000,
          "salePrice": 650000,
          "currency": "VND"
        },
        "description": "Vé VIP với nhiều ưu đãi đặc biệt",
        "maxQuantity": 15,
        "metadata": {
          "benefits": ["Tài liệu in cao cấp", "VIP lounge", "Private networking", "1-on-1 consultation", "Premium gift"],
          "validUntil": "2024-12-30"
        }
      }
    ]
  }
}

### Test Update Service Product
### Cập nhật dịch vụ với service packages mới

PUT {{baseUrl}}/user/products/4
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Dịch vụ thiết kế website - Updated",
  "price": {
    "listPrice": 6000000,
    "salePrice": 5000000,
    "currency": "VND"
  },
  "description": "Dịch vụ thiết kế website chuyên nghiệp cho doanh nghiệp - Phiên bản cập nhật",
  "tags": ["thiết kế", "website", "ui/ux", "frontend", "updated"],
  "advancedInfo": {
    "serviceTime": "9:00 AM - 6:00 PM",
    "serviceDuration": "4-6 tuần",
    "serviceProvider": "RedAI Design Team",
    "serviceType": "Web Development",
    "serviceLocation": "Remote + On-site",
    "servicePackages": [
      {
        "name": "Gói cơ bản - Updated",
        "price": {
          "listPrice": 3500000,
          "salePrice": 3000000,
          "currency": "VND"
        },
        "description": "Website cơ bản 5 trang - Cập nhật",
        "duration": "2 tuần",
        "features": ["5 trang", "Responsive design", "SEO cơ bản", "2 tháng support"],
        "metadata": {
          "deliveryTime": "14 days",
          "revisions": "3",
          "hosting": "1 year included"
        }
      },
      {
        "name": "Gói enterprise",
        "price": {
          "listPrice": 12000000,
          "salePrice": 10000000,
          "currency": "VND"
        },
        "description": "Website enterprise với tính năng nâng cao",
        "duration": "6-8 tuần",
        "features": ["Unlimited pages", "Custom CMS", "Advanced integrations", "API development", "12 tháng support"],
        "metadata": {
          "deliveryTime": "42 days",
          "revisions": "Unlimited",
          "hosting": "3 years included"
        }
      }
    ]
  }
}

### Test Update Combo Product
### Cập nhật combo với items mới

PUT {{baseUrl}}/user/products/5
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Combo khóa học lập trình Full-stack - Updated",
  "price": {
    "listPrice": 3500000,
    "salePrice": 2800000,
    "currency": "VND"
  },
  "description": "Combo khóa học lập trình Full-stack bao gồm Frontend, Backend và Database - Phiên bản cập nhật",
  "tags": ["combo", "lập trình", "fullstack", "khóa học", "updated"],
  "advancedInfo": {
    "comboType": "Education Bundle",
    "discountPercentage": 20,
    "validFrom": "2024-01-01",
    "validTo": "2024-12-31",
    "comboDescription": "Combo học lập trình toàn diện",
    "comboItems": [
      {
        "productId": 1,
        "productName": "Khóa học Frontend React - Updated",
        "quantity": 1,
        "originalPrice": 1400000,
        "discountedPrice": 1100000,
        "metadata": {
          "duration": "2.5 months",
          "level": "Beginner to Advanced"
        }
      },
      {
        "productId": 2,
        "productName": "Khóa học Backend Node.js - Updated",
        "quantity": 1,
        "originalPrice": 1600000,
        "discountedPrice": 1300000,
        "metadata": {
          "duration": "3 months",
          "level": "Intermediate to Advanced"
        }
      },
      {
        "productId": 3,
        "productName": "Khóa học Database PostgreSQL",
        "quantity": 1,
        "originalPrice": 900000,
        "discountedPrice": 700000,
        "metadata": {
          "duration": "1.5 months",
          "level": "Beginner to Intermediate"
        }
      }
    ]
  }
}

### Test Update Product with Classifications
### Cập nhật sản phẩm với classifications mới

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "classifications": [
    {
      "type": "Size M Premium",
      "price": {
        "listPrice": 380000,
        "salePrice": 320000,
        "currency": "VND"
      },
      "description": "Size M chất liệu premium - Updated",
      "metadata": {
        "size": "M",
        "material": "Cotton Premium",
        "origin": "Vietnam",
        "color": "Navy Blue"
      },
      "customFields": [
        {
          "fieldId": 1,
          "fieldValue": "Premium Collection 2024"
        }
      ]
    },
    {
      "type": "Size L Premium",
      "price": {
        "listPrice": 400000,
        "salePrice": 340000,
        "currency": "VND"
      },
      "description": "Size L chất liệu premium",
      "metadata": {
        "size": "L",
        "material": "Cotton Premium",
        "origin": "Vietnam",
        "color": "Black"
      }
    }
  ],
  "classificationsToDelete": [2, 3]
}

### Test Update Product with Custom Fields
### Cập nhật sản phẩm với custom fields

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "customFields": [
    {
      "fieldId": 1,
      "fieldValue": "Premium Collection 2024 - Updated"
    },
    {
      "fieldId": 2,
      "fieldValue": "Limited Edition"
    },
    {
      "fieldId": 3,
      "fieldValue": "Eco-friendly"
    }
  ]
}

### Test Update Product with Images
### Cập nhật sản phẩm với hình ảnh mới

PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "imagesMediaTypes": ["image/jpeg", "image/png", "image/webp"],
  "imageOperations": [
    {
      "operation": "ADD",
      "mediaType": "image/jpeg"
    },
    {
      "operation": "ADD", 
      "mediaType": "image/png"
    },
    {
      "operation": "DELETE",
      "position": 0
    }
  ]
}
