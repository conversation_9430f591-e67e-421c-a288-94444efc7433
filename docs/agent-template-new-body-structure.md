# Agent Template - <PERSON><PERSON><PERSON> Body Mới

## Tổng Quan

Tài liệu này mô tả cấu trúc body mới cho API tạo và cập nhật Agent Template với các thay đổi chính:

1. **Thay đổi `convertConfig`** từ object thành array với tên `conversion`
2. **Thêm field `modelSystemId`** thay vì `modelBaseId`
3. **Thêm field `memories`** để lưu trữ thông tin ghi nhớ

## Cấu Trúc Body Mới

### API Tạo Agent Template

```json
{
  "name": "System Assistant",
  "avatarMimeType": "image/jpeg",
  "modelConfig": {
    "temperature": 1,
    "top_p": 1,
    "top_k": 1,
    "max_tokens": 1000
  },
  "profile": {
    "gender": "MALE",
    "dateOfBirth": "1990-01-01",
    "position": "Developer",
    "education": "Bachelor",
    "skills": [
      "JavaScript",
      "Python"
    ],
    "personality": [
      "Creative",
      "Team-player"
    ],
    "languages": [
      "English",
      "Vietnamese"
    ],
    "nations": "Vietnam"
  },
  "instruction": "Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc",
  "vectorStoreId": "vector-store-1",
  "status": "DRAFT",
  "conversion": [
    {
      "name": "email",
      "type": "string",
      "description": "Địa chỉ email người dùng",
      "required": true,
      "active": true
    }
  ],
  "typeId": 1,
  "modelSystemId": "model-system-uuid",
  "isForSale": false,
  "memories": [
    {
      "content": "memories"
    }
  ]
}
```

## Chi Tiết Các Field Mới

### 1. Field `conversion` (Array)

Thay thế cho `convertConfig` object cũ, giờ là array với cấu trúc:

```typescript
interface ConversionConfig {
  name: string;           // Tên field
  type: string;          // Kiểu dữ liệu: 'string' | 'number' | 'boolean' | 'array_number' | 'array_string' | 'enum'
  description: string;   // Mô tả field
  required: boolean;     // Bắt buộc hay không
  active: boolean;       // Trạng thái hoạt động
}
```

**Ví dụ:**
```json
"conversion": [
  {
    "name": "email",
    "type": "string", 
    "description": "Địa chỉ email người dùng",
    "required": true,
    "active": true
  },
  {
    "name": "age",
    "type": "number",
    "description": "Tuổi của người dùng", 
    "required": false,
    "active": true
  }
]
```

### 2. Field `modelSystemId` (String)

Thay thế cho `modelBaseId`, liên kết với bảng `system_models`:

```json
"modelSystemId": "model-system-uuid"
```

### 3. Field `memories` (Array)

Lưu trữ thông tin ghi nhớ của agent:

```typescript
interface AgentMemory {
  content: string;  // Nội dung ghi nhớ
}
```

**Ví dụ:**
```json
"memories": [
  {
    "content": "Nhớ rằng người dùng thích giao tiếp bằng tiếng Việt"
  },
  {
    "content": "Luôn hỏi xác nhận trước khi thực hiện hành động quan trọng"
  }
]
```

## Validation Rules

### ConversionConfig Validation
- `name`: Required, string, không được rỗng
- `type`: Required, phải là một trong các giá trị: 'string', 'number', 'boolean', 'array_number', 'array_string', 'enum'
- `description`: Required, string, không được rỗng
- `required`: Required, boolean
- `active`: Required, boolean

### AgentMemory Validation
- `content`: Required, string, không được rỗng

### ModelSystemId Validation
- Optional, string, phải là UUID hợp lệ nếu có

## Backward Compatibility

Để đảm bảo tương thích ngược:

1. **Field `convertConfig`** vẫn được giữ lại (legacy)
2. **Field `modelBaseId`** vẫn được giữ lại (legacy)
3. Các field mới là optional, không bắt buộc

## Database Schema Changes

### Bảng `agents_template` - Các field mới:

```sql
-- Cấu hình conversion mới
ALTER TABLE agents_template ADD COLUMN conversion jsonb DEFAULT '[]'::jsonb;

-- ID system model
ALTER TABLE agents_template ADD COLUMN model_system_id uuid;

-- Memories của agent  
ALTER TABLE agents_template ADD COLUMN memories jsonb DEFAULT '[]'::jsonb;
```

## Migration

Chạy migration để thêm các field mới:

```bash
npm run migration:run
```

File migration: `src/migrations/1734567890123-add-agent-template-new-fields.ts`

## Ví Dụ Sử Dụng

### Tạo Agent Template với cấu trúc mới:

```bash
curl -X POST /api/admin/agent-templates \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "Customer Support Assistant",
    "modelConfig": {
      "temperature": 0.7,
      "top_p": 0.9,
      "max_tokens": 2000
    },
    "conversion": [
      {
        "name": "customer_name",
        "type": "string",
        "description": "Tên khách hàng",
        "required": true,
        "active": true
      },
      {
        "name": "issue_priority",
        "type": "enum", 
        "description": "Mức độ ưu tiên vấn đề",
        "required": false,
        "active": true
      }
    ],
    "typeId": 1,
    "modelSystemId": "123e4567-e89b-12d3-a456-426614174000",
    "memories": [
      {
        "content": "Luôn thể hiện sự đồng cảm với khách hàng"
      }
    ]
  }'
```

## Lưu Ý Quan Trọng

1. **Field `conversion`** ưu tiên hơn `convertConfig` khi cả hai đều có
2. **Field `modelSystemId`** ưu tiên hơn `modelBaseId` khi cả hai đều có  
3. **Field `memories`** có thể là array rỗng `[]`
4. Tất cả các field mới đều optional để đảm bảo tương thích ngược
